/* Custom styles for Bytewise Chat Application */

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #f9fafb;
}

.message {
    max-width: 80%;
    margin-bottom: 12px;
    line-height: 1.5;
}

.user-message {
    background-color: #f0f0f0;
    color: #333;
    border-radius: 18px 18px 4px 18px;
}

.assistant-message {
    background-color: #ffffff;
    color: #333;
    border: 1px solid #e5e7eb;
    border-radius: 18px 18px 18px 4px;
}

.upload-area {
    border: 2px dashed #d1d5db;
    transition: all 0.3s ease;
}

.upload-area:hover, .upload-area.active {
    border-color: #9ca3af;
    background-color: #f3f4f6;
}

.chat-container {
    height: calc(100vh - 140px);
}

.modal-overlay {
    transition: opacity 0.3s ease;
}

.modal-content {
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: translateY(20px);
    opacity: 0;
}

.modal-overlay.show {
    opacity: 1;
}

.modal-overlay.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

.file-preview {
    display: none;
    margin-top: 10px;
    padding: 8px;
    background-color: #f3f4f6;
    border-radius: 6px;
}

/* Textarea specific styling */
#message-input {
    transition: height 0.1s ease;
    overflow-y: auto;
    word-wrap: break-word;
    white-space: pre-wrap;
}

#message-input::placeholder {
    color: #9ca3af;
}

/* Ensure proper alignment in flex container */
.flex.items-end #message-input {
    align-self: stretch;
}